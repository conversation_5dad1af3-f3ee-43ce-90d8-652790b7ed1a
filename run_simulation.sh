#!/bin/bash

# Native Tiltrotor Simulation Runner
# This script handles the complete setup and launch of the tiltrotor simulation using native Gazebo

set -e

echo "=== Tiltrotor Drone Native Simulation Setup ==="

# Check if Gazebo is installed
if ! command -v gz &> /dev/null; then
    echo "ERROR: Gazebo (gz) is not installed or not in PATH."
    echo "Please install Gazebo Garden or newer."
    echo "Installation instructions:"
    echo "  Ubuntu: https://gazebosim.org/docs/garden/install_ubuntu"
    echo "  macOS: brew install gazebo"
    exit 1
fi

echo "Gazebo found: $(gz --version)"

# Parse command line arguments
GUI_MODE=true
HEADLESS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --headless)
            HEADLESS=true
            GUI_MODE=false
            shift
            ;;
        --gui)
            GUI_MODE=true
            HEADLESS=false
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--headless|--gui]"
            echo "  --headless  Run simulation without GUI"
            echo "  --gui       Run simulation with <PERSON><PERSON> (default)"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

# Validate model files
echo "Validating model files..."
if [ ! -f "drone_model/model.sdf" ]; then
    echo "ERROR: drone_model/model.sdf not found!"
    exit 1
fi

if [ ! -f "drone_model/meshes/body.dae" ]; then
    echo "ERROR: drone_model/meshes/body.dae not found!"
    exit 1
fi

if [ ! -f "worlds/tiltrotor_world.sdf" ]; then
    echo "ERROR: worlds/tiltrotor_world.sdf not found!"
    exit 1
fi

echo "Model files validated successfully."

# Set environment variables
export HEADLESS=$HEADLESS

# Start the appropriate simulation
if [ "$HEADLESS" = true ]; then
    echo "Starting headless simulation..."
    ./launch/launch_tiltrotor_sim.sh
else
    echo "Starting GUI simulation..."
    echo ""
    echo "To control the drone, use these commands in another terminal:"
    echo "  # Spin all propellers:"
    echo "  gz topic -t /command/motor_speed -m gz.msgs.Actuators -p 'velocity:[500, 500, 500, 500]'"
    echo ""
    echo "  # Tilt rotors (examples):"
    echo "  gz topic -t /model/tiltrotor_drone/joint/rear_left_tilt_joint/cmd_pos -m gz.msgs.Double -p 'data: 0.5'"
    echo "  gz topic -t /model/tiltrotor_drone/joint/rear_right_tilt_joint/cmd_pos -m gz.msgs.Double -p 'data: 0.5'"
    echo ""

    ./launch/launch_tiltrotor_sim.sh
fi
