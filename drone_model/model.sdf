<?xml version="1.0"?>
<sdf version="1.6">
    <model name="tiltrotor_drone">
        <pose frame="">0 0 0 -1.5 1.5 3.14</pose>
        <link name="base_link">
            <pose frame="">0 0 0 -1.5 1.5 3.14</pose>
            <inertial>
                <pose frame="">0 0 0 -1.5 1.5 3.14</pose>
                <mass>1.0</mass>
                <inertia>
                  <ixx>0.025</ixx>
                  <ixy>0</ixy>
                  <ixz>0</ixz>
                  <iyy>0.009</iyy>
                  <iyz>0</iyz>
                  <izz>0.033</izz>
                </inertia>
            </inertial>
            <collision name="base_link_inertia_collision">
                <pose frame="">0 0 0 -1.5 1.5 3.14</pose>
                <geometry>
                    <box>
                        <size>0.47 0.47 0.11</size>
                    </box>
                </geometry>
            </collision>
            <visual name="base_link_inertia_visual">
                <pose frame="">0 0 0 -1.5 1.5 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://drone_model/meshes/body.dae</uri>
                    </mesh>
                </geometry>
            </visual>
            <sensor name="air_pressure_sensor" type="air_pressure">
                <always_on>1</always_on>
                <update_rate>50</update_rate>
                <air_pressure>
                <pressure>
                    <noise type="gaussian">
                    <mean>0</mean>
                    <stddev>0.01</stddev>
                    </noise>
                </pressure>
                </air_pressure>
            </sensor>
            <sensor name="imu_sensor" type="imu">
                <always_on>1</always_on>
                <update_rate>250</update_rate>
                <imu>
                    <enable_orientation>0</enable_orientation>
                    <angular_velocity>
                        <x>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.009</stddev>
                                <bias_mean>0.00075</bias_mean>
                                <bias_stddev>0.005</bias_stddev>
                                <dynamic_bias_stddev>0.00002</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>400.0</dynamic_bias_correlation_time>
				<precision>0.00025</precision>
                            </noise>
                        </x>
                        <y>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.009</stddev>
                                <bias_mean>0.00075</bias_mean>
                                <bias_stddev>0.005</bias_stddev>
                                <dynamic_bias_stddev>0.00002</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>400.0</dynamic_bias_correlation_time>
				<precision>0.00025</precision>
                            </noise>
                        </y>
                        <z>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.009</stddev>
                                <bias_mean>0.00075</bias_mean>
                                <bias_stddev>0.005</bias_stddev>
                                <dynamic_bias_stddev>0.00002</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>400.0</dynamic_bias_correlation_time>
				<precision>0.00025</precision>
                            </noise>
                        </z>
                    </angular_velocity>
                    <linear_acceleration>
                        <x>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.021</stddev>
                                <bias_mean>0.05</bias_mean>
                                <bias_stddev>0.0075</bias_stddev>
                                <dynamic_bias_stddev>0.000375</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>175.0</dynamic_bias_correlation_time>
				<precision>0.005</precision>
                            </noise>
                        </x>
                        <y>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.021</stddev>
                                <bias_mean>0.05</bias_mean>
                                <bias_stddev>0.0075</bias_stddev>
                                <dynamic_bias_stddev>0.000375</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>175.0</dynamic_bias_correlation_time>
				<precision>0.005</precision>
                            </noise>
                        </y>
                        <z>
                            <noise type="gaussian">
                                <mean>0</mean>
                                <stddev>0.021</stddev>
                                <bias_mean>0.05</bias_mean>
                                <bias_stddev>0.0075</bias_stddev>
                                <dynamic_bias_stddev>0.000375</dynamic_bias_stddev>
				<dynamic_bias_correlation_time>175.0</dynamic_bias_correlation_time>
				<precision>0.005</precision>
                            </noise>
                        </z>
                    </linear_acceleration>
                </imu>

            </sensor>
            <sensor name="air_pressure_sensor_2" type="air_pressure">
                <always_on>1</always_on>
                <update_rate>20</update_rate>
                <air_pressure>
                    <reference_altitude>0</reference_altitude>
                    <noise type="gaussian">
                        <mean>0.00000008</mean>
                    </noise>
                </air_pressure>
            </sensor>
            <sensor name="magnetometer" type="magnetometer">
                <always_on>1</always_on>
                <update_rate>20</update_rate>
                <magnetometer>
                    <x>
                        <noise type="gaussian">
                            <mean>0.000000080</mean>
                            <bias_mean>0.000000400</bias_mean>
                        </noise>
                    </x>
                    <y>
                        <noise type="gaussian">
                            <mean>0.000000080</mean>
                            <bias_mean>0.000000400</bias_mean>
                        </noise>
                    </y>
                    <z>
                        <noise type="gaussian">
                            <mean>0.000000080</mean>
                            <bias_mean>0.000000400</bias_mean>
                        </noise>
                    </z>
                </magnetometer>
            </sensor>


        </link>

        <!-- Rear Left Motor Assembly (Red) -->
        <link name="rear_left_motor_assembly">
            <pose frame="">-0.3 0 -0.05 0 0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 0 0</pose>
                <mass>0.15</mass>
                <inertia>
                    <ixx>0.002</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>0.002</iyy>
                    <iyz>0</iyz>
                    <izz>0.001</izz>
                </inertia>
            </inertial>
            <visual name="rear_left_motor_assembly_visual">
                <pose frame="">0 0 0 0 0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://drone_model/meshes/tilt_rotor_1.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Red</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <gravity>1</gravity>
        </link>

        <!-- Rear Left Tilt Joint -->
        <joint name="rear_left_tilt_joint" type="revolute">
            <child>rear_left_motor_assembly</child>
            <parent>base_link</parent>
            <axis>
                <xyz>0 1 0</xyz>
                <limit>
                    <lower>-1.57</lower>
                    <upper>1.57</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Rear Right Motor Assembly (Green) -->
        <link name="rear_right_motor_assembly">
            <pose frame="">0.3 0 -0.05 0 0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 0 0</pose>
                <mass>0.15</mass>
                <inertia>
                    <ixx>0.002</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>0.002</iyy>
                    <iyz>0</iyz>
                    <izz>0.001</izz>
                </inertia>
            </inertial>
            <visual name="rear_right_motor_assembly_visual">
                <pose frame="">0 0 0 0 0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://drone_model/meshes/tilt_rotor_2.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Green</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <gravity>1</gravity>
        </link>

        <!-- Rear Right Tilt Joint -->
        <joint name="rear_right_tilt_joint" type="revolute">
            <child>rear_right_motor_assembly</child>
            <parent>base_link</parent>
            <axis>
                <xyz>0 1 0</xyz>
                <limit>
                    <lower>-1.57</lower>
                    <upper>1.57</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Front Left Motor Assembly (Blue) -->
        <link name="front_left_motor_assembly">
            <pose frame="">-0.2 0.2 -0.05 0 0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 0 0</pose>
                <mass>0.15</mass>
                <inertia>
                    <ixx>0.002</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>0.002</iyy>
                    <iyz>0</iyz>
                    <izz>0.001</izz>
                </inertia>
            </inertial>
            <visual name="front_left_motor_assembly_visual">
                <pose frame="">0 0 0 0 0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://drone_model/meshes/tilt_rotor_1.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Blue</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <gravity>1</gravity>
        </link>

        <!-- Front Left Tilt Joint -->
        <joint name="front_left_tilt_joint" type="revolute">
            <child>front_left_motor_assembly</child>
            <parent>base_link</parent>
            <axis>
                <xyz>0 1 0</xyz>
                <limit>
                    <lower>-1.57</lower>
                    <upper>1.57</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Front Right Motor Assembly (Yellow) -->
        <link name="front_right_motor_assembly">
            <pose frame="">0.2 0.2 -0.05 0 0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 0 0</pose>
                <mass>0.15</mass>
                <inertia>
                    <ixx>0.002</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>0.002</iyy>
                    <iyz>0</iyz>
                    <izz>0.001</izz>
                </inertia>
            </inertial>
            <visual name="front_right_motor_assembly_visual">
                <pose frame="">0 0 0 0 0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://drone_model/meshes/tilt_rotor_2.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Yellow</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
            <gravity>1</gravity>
        </link>

        <!-- Front Right Tilt Joint -->
        <joint name="front_right_tilt_joint" type="revolute">
            <child>front_right_motor_assembly</child>
            <parent>base_link</parent>
            <axis>
                <xyz>0 1 0</xyz>
                <limit>
                    <lower>-1.57</lower>
                    <upper>1.57</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Rear Left Propeller (Red) -->
        <link name="rear_left_propeller">
            <pose frame="">-0.3 0 -0.05 0 -0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 -0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <collision name="rear_left_propeller_collision">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <cylinder>
                        <length>0.005</length>
                        <radius>0.1</radius>
                    </cylinder>
                </geometry>
                <surface>
                    <contact>
                        <ode/>
                    </contact>
                    <friction>
                        <ode/>
                    </friction>
                </surface>
            </collision>
            <visual name="rear_left_propeller_visual">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://drone_model/meshes/propeller_ccw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <diffuse>1 0 0 1</diffuse>
                    <script>
                        <name>Gazebo/Red</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
                <cast_shadows>0</cast_shadows>
            </visual>
            <gravity>1</gravity>
            <velocity_decay/>
        </link>
        <joint name="rear_left_propeller_joint" type="revolute">
            <child>rear_left_propeller</child>
            <parent>rear_left_motor_assembly</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Rear Right Propeller (Green) -->
        <link name="rear_right_propeller">
            <pose frame="">0.3 0 -0.05 0 -0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 -0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <collision name="rear_right_propeller_collision">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <cylinder>
                        <length>0.005</length>
                        <radius>0.1</radius>
                    </cylinder>
                </geometry>
                <surface>
                    <contact>
                        <ode/>
                    </contact>
                    <friction>
                        <ode/>
                    </friction>
                </surface>
            </collision>
            <visual name="rear_right_propeller_visual">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://drone_model/meshes/propeller_ccw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <diffuse>0 1 0 1</diffuse>
                    <script>
                        <name>Gazebo/Green</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
                <cast_shadows>0</cast_shadows>
            </visual>
            <gravity>1</gravity>
            <velocity_decay/>
        </link>
        <joint name="rear_right_propeller_joint" type="revolute">
            <child>rear_right_propeller</child>
            <parent>rear_right_motor_assembly</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Front Left Propeller (Blue) -->
        <link name="front_left_propeller">
            <pose frame="">-0.2 0.2 -0.05 0 -0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 -0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <collision name="front_left_propeller_collision">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <cylinder>
                        <length>0.005</length>
                        <radius>0.1</radius>
                    </cylinder>
                </geometry>
                <surface>
                    <contact>
                        <ode/>
                    </contact>
                    <friction>
                        <ode/>
                    </friction>
                </surface>
            </collision>
            <visual name="front_left_propeller_visual">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://drone_model/meshes/propeller_cw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <diffuse>0 0 1 1</diffuse>
                    <script>
                        <name>Gazebo/Blue</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
                <cast_shadows>0</cast_shadows>
            </visual>
            <gravity>1</gravity>
            <velocity_decay/>
        </link>
        <joint name="front_left_propeller_joint" type="revolute">
            <child>front_left_propeller</child>
            <parent>front_left_motor_assembly</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Front Right Propeller (Yellow) -->
        <link name="front_right_propeller">
            <pose frame="">0.2 0.2 -0.05 0 -0 0</pose>
            <inertial>
                <pose frame="">0 0 0 0 -0 0</pose>
                <mass>0.005</mass>
                <inertia>
                    <ixx>9.75e-07</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>4.17041e-05</iyy>
                    <iyz>0</iyz>
                    <izz>4.26041e-05</izz>
                </inertia>
            </inertial>
            <collision name="front_right_propeller_collision">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <cylinder>
                        <length>0.005</length>
                        <radius>0.1</radius>
                    </cylinder>
                </geometry>
                <surface>
                    <contact>
                        <ode/>
                    </contact>
                    <friction>
                        <ode/>
                    </friction>
                </surface>
            </collision>
            <visual name="front_right_propeller_visual">
                <pose frame="">0 0 0 0 -0 0</pose>
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://drone_model/meshes/propeller_cw.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <diffuse>1 1 0 1</diffuse>
                    <script>
                        <name>Gazebo/Yellow</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
                <cast_shadows>0</cast_shadows>
            </visual>
            <gravity>1</gravity>
            <velocity_decay/>
        </link>
        <joint name="front_right_propeller_joint" type="revolute">
            <child>front_right_propeller</child>
            <parent>front_right_motor_assembly</parent>
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
                <dynamics>
                    <spring_reference>0</spring_reference>
                    <spring_stiffness>0</spring_stiffness>
                </dynamics>
                <use_parent_model_frame>1</use_parent_model_frame>
            </axis>
        </joint>

        <!-- Motor Plugins -->
        <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>rear_left_propeller_joint</jointName>
            <linkName>rear_left_propeller</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>0</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>
        <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>rear_right_propeller_joint</jointName>
            <linkName>rear_right_propeller</linkName>
            <turningDirection>cw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>1</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>
        <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>front_left_propeller_joint</jointName>
            <linkName>front_left_propeller</linkName>
            <turningDirection>cw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>2</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>
        <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
            <jointName>front_right_propeller_joint</jointName>
            <linkName>front_right_propeller</linkName>
            <turningDirection>cw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1000.0</maxRotVelocity>
            <motorConstant>8.54858e-06</motorConstant>
            <momentConstant>0.016</momentConstant>
            <commandSubTopic>command/motor_speed</commandSubTopic>
            <motorNumber>3</motorNumber>
            <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
            <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
            <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
            <motorType>velocity</motorType>
        </plugin>

        <!-- Tilt Control Plugins -->
        <plugin filename="gz-sim-joint-position-controller-system" name="gz::sim::systems::JointPositionController">
            <joint_name>rear_left_tilt_joint</joint_name>
            <topic>/model/tiltrotor_drone/joint/rear_left_tilt_joint/cmd_pos</topic>
            <p_gain>10</p_gain>
            <i_gain>0.1</i_gain>
            <d_gain>0.5</d_gain>
        </plugin>

        <plugin filename="gz-sim-joint-position-controller-system" name="gz::sim::systems::JointPositionController">
            <joint_name>rear_right_tilt_joint</joint_name>
            <topic>/model/tiltrotor_drone/joint/rear_right_tilt_joint/cmd_pos</topic>
            <p_gain>10</p_gain>
            <i_gain>0.1</i_gain>
            <d_gain>0.5</d_gain>
        </plugin>

        <plugin filename="gz-sim-joint-position-controller-system" name="gz::sim::systems::JointPositionController">
            <joint_name>front_left_tilt_joint</joint_name>
            <topic>/model/tiltrotor_drone/joint/front_left_tilt_joint/cmd_pos</topic>
            <p_gain>10</p_gain>
            <i_gain>0.1</i_gain>
            <d_gain>0.5</d_gain>
        </plugin>

        <plugin filename="gz-sim-joint-position-controller-system" name="gz::sim::systems::JointPositionController">
            <joint_name>front_right_tilt_joint</joint_name>
            <topic>/model/tiltrotor_drone/joint/front_right_tilt_joint/cmd_pos</topic>
            <p_gain>10</p_gain>
            <i_gain>0.1</i_gain>
            <d_gain>0.5</d_gain>
        </plugin>
    </model>
</sdf>
